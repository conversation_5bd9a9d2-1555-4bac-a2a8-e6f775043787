"use client";

import Link from "next/link";
import Image from "next/image";

import { Grad<PERSON>Button } from "@/components/ui/gradient-button";
import { HorizontalScroller } from "@/components/common/HorizontalScroller";
import { motion } from "framer-motion";
import {
  CheckIcon,
  ClockIcon,
  ShieldIcon,
  DollarSignIcon,
  ChevronRightIcon
} from "@/components/ui/icons";
import { FeatureCard } from "@/components/ui/feature-card";
import { SolutionCard } from "@/components/ui/solution-card";
import { OverlayCard } from "@/components/ui/overlay-card";

function FloatingPaths({ position }: { position: number }) {
    const paths = Array.from({ length: 36 }, (_, i) => ({
        id: i,
        d: `M-${380 - i * 5 * position} -${289 + i * 6}C-${
            380 - i * 5 * position
        } -${289 + i * 6} -${312 - i * 5 * position} ${116 - i * 6} ${
            152 - i * 5 * position
        } ${243 - i * 6}C${616 - i * 5 * position} ${370 - i * 6} ${
            684 - i * 5 * position
        } ${775 - i * 6} ${684 - i * 5 * position} ${775 - i * 6}`,
        color: `rgba(15,23,42,${0.1 + i * 0.03})`,
        width: 0.5 + i * 0.03,
    }));

    return (
        <div className="absolute inset-0 pointer-events-none">
            <svg
                className="w-full h-full"
                viewBox="0 0 696 316"
                fill="none"
            >
                <title>Background Paths</title>
                {paths.map((path) => (
                    <motion.path
                        key={path.id}
                        d={path.d}
                        stroke="#D4C4A8"
                        strokeWidth={path.width}
                        strokeOpacity={0.08 + path.id * 0.02}
                        initial={{ pathLength: 0.3, opacity: 0.4 }}
                        animate={{
                            pathLength: 1,
                            opacity: [0.2, 0.4, 0.2],
                            pathOffset: [0, 1, 0],
                        }}
                        transition={{
                            duration: 20 + Math.random() * 10,
                            repeat: Number.POSITIVE_INFINITY,
                            ease: "linear",
                        }}
                    />
                ))}
            </svg>
        </div>
    );
}

// Herausforderungen und Lösungen Daten
const challengesAndSolutions = [
  {
    title: "Komplexe Logistik",
    description: "Unsere Plattform vereinfacht den gesamten Prozess mit intelligenter Routenplanung und Echtzeit-Tracking.",
    icon: "🔄",
    color: "bg-ym-light-green",
  },
  {
    title: "Mangelnde Transparenz",
    description: "Vollständige Transparenz durch Live-Tracking und detaillierte Statusupdates in Echtzeit.",
    icon: "📊",
    color: "bg-ym-medium-green",
  },
  {
    title: "Hohe Kosten",
    description: "Kostenoptimierung durch intelligente Routenplanung und ein flexibles Fahrernetzwerk.",
    icon: "💰",
    color: "bg-ym-light-green",
  },
  {
    title: "Qualitätssicherung",
    description: "Sorgfältig geprüfte Fahrer und ein umfassendes Bewertungssystem für konstant hohe Qualität.",
    icon: "✓",
    color: "bg-ym-medium-green",
  },
];

// Branchenlösungen
const industrySolutions = [
  {
    id: "car-rental",
    title: "Autovermietungen",
    description: "Optimieren Sie Ihre Fahrzeuglogistik mit unserer spezialisierten Lösung für Autovermietungen.",
    imageUrl: "/placeholder-fullscreen-1.jpg",
  },
  {
    id: "leasing",
    title: "Leasing-Unternehmen",
    description: "Steigern Sie die Effizienz Ihrer Fahrzeugübergaben und -rücknahmen mit unserer Leasing-Lösung.",
    imageUrl: "/placeholder-fullscreen-2.jpg",
  },
  {
    id: "dealerships",
    title: "Autohändler",
    description: "Vereinfachen Sie den Fahrzeugtransport zwischen Standorten, Auktionen und Kunden.",
    imageUrl: "/placeholder-fullscreen-3.jpg",
  },
  {
    id: "fleet",
    title: "Flottenmanagement",
    description: "Optimieren Sie die Logistik Ihrer Unternehmensflotte mit unserer spezialisierten Lösung.",
    imageUrl: "/placeholder-fullscreen-4.jpg",
  },
];

export function HomePageContent() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-black overflow-hidden">
        <FloatingPaths position={1} />
        <div className="container mx-auto px-4 py-20 md:py-32 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              className="space-y-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                Innovative <span style={{ color: '#F8E7D1' }}>Fahrzeugüberführungen</span> für Ihr Unternehmen
              </h1>
              <p className="text-lg md:text-xl text-white max-w-lg">
                YoungMobility verbindet Geschäftskunden mit qualifizierten Fahrern für zuverlässige und effiziente Fahrzeugüberführungen.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <GradientButton asChild>
                  <Link href="/register-client">
                    Als Unternehmen registrieren
                  </Link>
                </GradientButton>
                <GradientButton variant="variant" asChild>
                  <Link href="/register-driver">
                    Als Fahrer bewerben
                  </Link>
                </GradientButton>
              </div>
              <div className="flex items-center space-x-2 text-sm text-white">
                <CheckIcon className="h-5 w-5" style={{ color: '#F8E7D1' }} />
                <span>Über 10.000 erfolgreiche Überführungen</span>
              </div>
            </motion.div>
            <motion.div
              className="relative flex justify-end"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="relative rounded-2xl overflow-hidden shadow-xl w-[395px] lg:w-[495px] xl:w-[535px] h-[400px] lg:h-[450px] xl:h-[500px]">
                <Image
                  src="/RR-2.jpg"
                  alt="Rolls Royce Fahrzeugüberführung"
                  fill
                  className="object-cover object-center"
                  priority
                />
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-black">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4" style={{ color: '#F8E7D1' }}>
              Warum YoungMobility?
            </h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Unsere Plattform bietet innovative Lösungen für die moderne Fahrzeuglogistik
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <FeatureCard
              icon={<ClockIcon style={{ color: '#F8E7D1' }} />}
              title="Zeitsparend"
              description="Reduzieren Sie den administrativen Aufwand und finden Sie schnell qualifizierte Fahrer für Ihre Überführungen."
              delay={0.1}
            />

            <FeatureCard
              icon={<ShieldIcon style={{ color: '#F8E7D1' }} />}
              title="Zuverlässig"
              description="Alle Fahrer werden sorgfältig geprüft und bewertet, um höchste Qualität und Sicherheit zu gewährleisten."
              delay={0.2}
            />

            <FeatureCard
              icon={<DollarSignIcon style={{ color: '#F8E7D1' }} />}
              title="Kostengünstig"
              description="Transparente Preisgestaltung und optimierte Prozesse sparen Ihnen Zeit und Geld bei jeder Überführung."
              delay={0.3}
            />
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <section className="py-20 bg-black relative overflow-hidden">
        <FloatingPaths position={1} />
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Für wen ist YoungMobility?</h2>
            <p className="text-lg text-[#F8E7D1] max-w-2xl mx-auto">
              Unsere Plattform bietet maßgeschneiderte Lösungen für verschiedene Branchen
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="relative rounded-xl overflow-hidden shadow-2xl group border border-gray-800 hover:border-gray-700 transition-all duration-300">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/70 z-10"></div>
              <Image
                src="/placeholder-fullscreen-6.jpg"
                alt="Für Unternehmen"
                width={600}
                height={400}
                className="w-full h-[300px] object-cover transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute bottom-0 left-0 right-0 p-6 z-20">
                <h3 className="text-2xl font-bold text-white mb-2">Für Unternehmen</h3>
                <p className="text-[#F8E7D1] mb-4">
                  Optimieren Sie Ihre Fahrzeuglogistik mit unserer effizienten und transparenten Plattform.
                </p>
                <GradientButton asChild>
                  <Link href="/register-client">
                    Jetzt registrieren
                  </Link>
                </GradientButton>
              </div>
            </div>

            <div className="relative rounded-xl overflow-hidden shadow-2xl group border border-gray-800 hover:border-gray-700 transition-all duration-300">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/70 z-10"></div>
              <Image
                src="/placeholder-fullscreen-7.jpg"
                alt="Für Fahrer"
                width={600}
                height={400}
                className="w-full h-[300px] object-cover transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute bottom-0 left-0 right-0 p-6 z-20">
                <h3 className="text-2xl font-bold text-white mb-2">Für Fahrer</h3>
                <p className="text-[#F8E7D1] mb-4">
                  Werden Sie Teil unseres Netzwerks und verdienen Sie flexibel mit Fahrzeugüberführungen.
                </p>
                <GradientButton variant="variant" asChild>
                  <Link href="/youngmovers">
                    Mehr erfahren
                  </Link>
                </GradientButton>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Industry Solutions Section */}
      <section className="py-20 bg-black relative overflow-hidden">
        <FloatingPaths position={2} />
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Branchenlösungen</h2>
            <p className="text-lg text-[#F8E7D1] max-w-2xl mx-auto">
              Entdecken Sie unsere maßgeschneiderten Lösungen für verschiedene Branchen
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {industrySolutions.map((solution, index) => (
              <SolutionCard
                key={solution.id}
                id={solution.id}
                title={solution.title}
                description={solution.description}
                imageUrl={solution.imageUrl}
                delay={0.1 * index}
              />
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary/5 relative overflow-hidden">
        <div className="absolute top-0 right-0 w-1/3 h-full bg-primary/10 transform -skew-x-12"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <motion.h2
              className="text-3xl md:text-4xl font-bold text-gray-900 mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              Bereit, Ihre Fahrzeuglogistik zu revolutionieren?
            </motion.h2>
            <motion.p
              className="text-lg text-gray-600 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Registrieren Sie sich noch heute und erleben Sie, wie YoungMobility Ihre Fahrzeugüberführungen effizienter, transparenter und kostengünstiger macht.
            </motion.p>
            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <GradientButton asChild>
                <Link href="/register-client">
                  Jetzt registrieren
                </Link>
              </GradientButton>
              <GradientButton variant="variant" asChild>
                <Link href="/contact">
                  Kontakt aufnehmen
                </Link>
              </GradientButton>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Development Links - Only for testing */}
      <section className="py-10 px-4 bg-gray-100 border-t border-gray-200">
        <div className="container mx-auto">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">Entwicklungsbereich</h2>
          <p className="text-sm text-gray-600 mb-6">Schnellzugriff auf implementierte Seiten (nur für Entwicklung):</p>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            <Link href="/client-dashboard" className="ym-ui-button-ghost py-2 px-3 text-center text-sm w-full">
              Dashboard
            </Link>
            <Link href="/client-orders" className="ym-ui-button-ghost py-2 px-3 text-center text-sm w-full">
              Aufträge
            </Link>
            <Link href="/client-orders-create" className="ym-ui-button-ghost py-2 px-3 text-center text-sm w-full">
              Neuer Auftrag
            </Link>
            <Link href="/client-billing" className="ym-ui-button-ghost py-2 px-3 text-center text-sm w-full">
              Rechnungen
            </Link>
            <Link href="/test" className="ym-ui-button-ghost py-2 px-3 text-center text-sm w-full">
              UI-Komponenten
            </Link>
            <Link href="/simple-test" className="ym-ui-button-ghost py-2 px-3 text-center text-sm w-full">
              Testseite
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
