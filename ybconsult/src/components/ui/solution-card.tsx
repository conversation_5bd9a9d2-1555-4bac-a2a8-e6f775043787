"use client";

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ChevronRightIcon } from './icons';

interface SolutionCardProps {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  delay?: number;
}

export const SolutionCard: React.FC<SolutionCardProps> = ({
  id,
  title,
  description,
  imageUrl,
  delay = 0
}) => {
  return (
    <motion.div
      className="bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
    >
      <div className="relative h-48">
        <Image
          src={imageUrl}
          alt={title}
          width={400}
          height={200}
          className="w-full h-full object-cover"
        />
      </div>
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 text-sm mb-4">{description}</p>
        <Link
          href={`/solutions/${id}`}
          className="text-primary font-medium text-sm flex items-center transition-colors hover:text-primary/80"
        >
          Mehr erfahren
          <ChevronRightIcon className="w-4 h-4 ml-1" />
        </Link>
      </div>
    </motion.div>
  );
};
